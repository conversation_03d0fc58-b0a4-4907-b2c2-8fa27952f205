using System;
using NUnit.Framework;
using Moq;
using SmartConnector.Core.Interfaces;
using SmartConnector.Ews.Interfaces;
using WeatherExtension.Processors;

namespace WeatherExtension.Tests.Processors
{
    [TestFixture]
    public class WeatherUpdateProcessorTests
    {
        private WeatherUpdateProcessor _processor;
        private Mock<IProcessorContext> _mockContext;
        private Mock<IEwsServerManager> _mockEwsServerManager;
        private Mock<ILogger> _mockLogger;

        [SetUp]
        public void SetUp()
        {
            _processor = new WeatherUpdateProcessor();
            _mockContext = new Mock<IProcessorContext>();
            _mockEwsServerManager = new Mock<IEwsServerManager>();
            _mockLogger = new Mock<ILogger>();

            _mockContext.Setup(c => c.GetService<IEwsServerManager>())
                       .Returns(_mockEwsServerManager.Object);
            _mockContext.Setup(c => c.GetService<ILogger>())
                       .Returns(_mockLogger.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _processor?.Dispose();
        }

        [Test]
        public void Initialize_WithValidContextAndApiKey_SetsUpServices()
        {
            // Arrange
            _processor.ApiKey = "test-api-key";

            // Act & Assert
            Assert.DoesNotThrow(() => _processor.Initialize(_mockContext.Object));
        }

        [Test]
        public void Initialize_WithNullEwsServerManager_ThrowsInvalidOperationException()
        {
            // Arrange
            _processor.ApiKey = "test-api-key";
            _mockContext.Setup(c => c.GetService<IEwsServerManager>())
                       .Returns((IEwsServerManager)null);

            // Act & Assert
            var ex = Assert.Throws<InvalidOperationException>(
                () => _processor.Initialize(_mockContext.Object));
            
            Assert.That(ex.Message, Does.Contain("EWS Server Manager service not available"));
        }

        [Test]
        public void Initialize_WithNullLogger_ThrowsInvalidOperationException()
        {
            // Arrange
            _processor.ApiKey = "test-api-key";
            _mockContext.Setup(c => c.GetService<ILogger>())
                       .Returns((ILogger)null);

            // Act & Assert
            var ex = Assert.Throws<InvalidOperationException>(
                () => _processor.Initialize(_mockContext.Object));
            
            Assert.That(ex.Message, Does.Contain("Logger service not available"));
        }

        [Test]
        public void Initialize_WithNullApiKey_ThrowsInvalidOperationException()
        {
            // Arrange
            _processor.ApiKey = null;

            // Act & Assert
            var ex = Assert.Throws<InvalidOperationException>(
                () => _processor.Initialize(_mockContext.Object));
            
            Assert.That(ex.Message, Does.Contain("OpenWeatherMap API key is required"));
        }

        [Test]
        public void Initialize_WithEmptyApiKey_ThrowsInvalidOperationException()
        {
            // Arrange
            _processor.ApiKey = "";

            // Act & Assert
            var ex = Assert.Throws<InvalidOperationException>(
                () => _processor.Initialize(_mockContext.Object));
            
            Assert.That(ex.Message, Does.Contain("OpenWeatherMap API key is required"));
        }

        [Test]
        public void Initialize_WithWhitespaceApiKey_ThrowsInvalidOperationException()
        {
            // Arrange
            _processor.ApiKey = "   ";

            // Act & Assert
            var ex = Assert.Throws<InvalidOperationException>(
                () => _processor.Initialize(_mockContext.Object));
            
            Assert.That(ex.Message, Does.Contain("OpenWeatherMap API key is required"));
        }

        [Test]
        public void Execute_WithNonExistentServer_ReturnsFailure()
        {
            // Arrange
            _processor.ApiKey = "test-api-key";
            _processor.Initialize(_mockContext.Object);
            _processor.ServerName = "NonExistentServer";
            
            _mockEwsServerManager.Setup(m => m.ServerExists("NonExistentServer"))
                                .Returns(false);

            // Act
            var result = _processor.Execute();

            // Assert
            Assert.IsFalse(result.Success);
            Assert.That(result.Message, Does.Contain("not found"));
        }

        [Test]
        public void Execute_WithExistingServerButNoApiAccess_CompletesWithWarnings()
        {
            // Arrange
            _processor.ApiKey = "invalid-api-key";
            _processor.Initialize(_mockContext.Object);
            _processor.ServerName = "TestServer";
            _processor.Cities = "London";
            
            _mockEwsServerManager.Setup(m => m.ServerExists("TestServer"))
                                .Returns(true);

            // Act
            var result = _processor.Execute();

            // Assert
            // The processor should complete even if API calls fail
            Assert.IsTrue(result.Success);
            _mockLogger.Verify(l => l.Error(It.IsAny<string>(), It.IsAny<Exception>()), Times.AtLeast(1));
        }

        [Test]
        public void Execute_WithEmptyCities_CompletesSuccessfully()
        {
            // Arrange
            _processor.ApiKey = "test-api-key";
            _processor.Initialize(_mockContext.Object);
            _processor.Cities = "";
            
            _mockEwsServerManager.Setup(m => m.ServerExists(It.IsAny<string>()))
                                .Returns(true);

            // Act
            var result = _processor.Execute();

            // Assert
            Assert.IsTrue(result.Success);
            Assert.That(result.Message, Does.Contain("0 cities"));
        }

        [Test]
        public void Execute_WithException_ReturnsFailureResult()
        {
            // Arrange
            _processor.ApiKey = "test-api-key";
            _processor.Initialize(_mockContext.Object);
            
            _mockEwsServerManager.Setup(m => m.ServerExists(It.IsAny<string>()))
                                .Throws(new Exception("Test exception"));

            // Act
            var result = _processor.Execute();

            // Assert
            Assert.IsFalse(result.Success);
            Assert.That(result.Message, Does.Contain("Update failed"));
            _mockLogger.Verify(l => l.Error(It.IsAny<string>(), It.IsAny<Exception>()), Times.Once);
        }

        [Test]
        public void DefaultProperties_HaveExpectedValues()
        {
            // Assert
            Assert.That(_processor.ServerName, Is.EqualTo("WeatherServer"));
            Assert.That(_processor.Cities, Is.EqualTo("London,New York,Tokyo"));
            Assert.That(_processor.Units, Is.EqualTo("metric"));
            Assert.That(_processor.UpdateIntervalMinutes, Is.EqualTo(15));
        }

        [Test]
        public void Dispose_DisposesWeatherService()
        {
            // Arrange
            _processor.ApiKey = "test-api-key";
            _processor.Initialize(_mockContext.Object);

            // Act & Assert
            Assert.DoesNotThrow(() => _processor.Dispose());
        }
    }
}
