using System;
using NUnit.Framework;
using Moq;
using SmartConnector.Core.Interfaces;
using SmartConnector.Ews.Interfaces;
using WeatherExtension.Processors;

namespace WeatherExtension.Tests.Processors
{
    [TestFixture]
    public class WeatherSetupProcessorTests
    {
        private WeatherSetupProcessor _processor;
        private Mock<IProcessorContext> _mockContext;
        private Mock<IEwsServerManager> _mockEwsServerManager;
        private Mock<ILogger> _mockLogger;

        [SetUp]
        public void SetUp()
        {
            _processor = new WeatherSetupProcessor();
            _mockContext = new Mock<IProcessorContext>();
            _mockEwsServerManager = new Mock<IEwsServerManager>();
            _mockLogger = new Mock<ILogger>();

            _mockContext.Setup(c => c.GetService<IEwsServerManager>())
                       .Returns(_mockEwsServerManager.Object);
            _mockContext.Setup(c => c.GetService<ILogger>())
                       .Returns(_mockLogger.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _processor?.Dispose();
        }

        [Test]
        public void Initialize_WithValidContext_SetsUpServices()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => _processor.Initialize(_mockContext.Object));
        }

        [Test]
        public void Initialize_WithNullEwsServerManager_ThrowsInvalidOperationException()
        {
            // Arrange
            _mockContext.Setup(c => c.GetService<IEwsServerManager>())
                       .Returns((IEwsServerManager)null);

            // Act & Assert
            var ex = Assert.Throws<InvalidOperationException>(
                () => _processor.Initialize(_mockContext.Object));
            
            Assert.That(ex.Message, Does.Contain("EWS Server Manager service not available"));
        }

        [Test]
        public void Initialize_WithNullLogger_ThrowsInvalidOperationException()
        {
            // Arrange
            _mockContext.Setup(c => c.GetService<ILogger>())
                       .Returns((ILogger)null);

            // Act & Assert
            var ex = Assert.Throws<InvalidOperationException>(
                () => _processor.Initialize(_mockContext.Object));
            
            Assert.That(ex.Message, Does.Contain("Logger service not available"));
        }

        [Test]
        public void Execute_WithNewServer_CreatesServerAndDataPoints()
        {
            // Arrange
            _processor.Initialize(_mockContext.Object);
            _processor.ServerName = "TestWeatherServer";
            _processor.Cities = "London,Paris";
            
            _mockEwsServerManager.Setup(m => m.ServerExists("TestWeatherServer"))
                                .Returns(false);

            // Act
            var result = _processor.Execute();

            // Assert
            Assert.IsTrue(result.Success);
            Assert.That(result.Message, Does.Contain("setup completed successfully"));
            
            _mockEwsServerManager.Verify(m => m.CreateServer(It.IsAny<EwsServerConfiguration>()), Times.Once);
            _mockEwsServerManager.Verify(m => m.AddDataPoint(It.IsAny<string>(), It.IsAny<EwsDataPoint>()), Times.AtLeast(14)); // 7 data points per city * 2 cities
        }

        [Test]
        public void Execute_WithExistingServer_UpdatesDataStructure()
        {
            // Arrange
            _processor.Initialize(_mockContext.Object);
            _processor.ServerName = "ExistingWeatherServer";
            _processor.Cities = "Tokyo";
            
            _mockEwsServerManager.Setup(m => m.ServerExists("ExistingWeatherServer"))
                                .Returns(true);

            // Act
            var result = _processor.Execute();

            // Assert
            Assert.IsTrue(result.Success);
            
            _mockEwsServerManager.Verify(m => m.CreateServer(It.IsAny<EwsServerConfiguration>()), Times.Never);
            _mockEwsServerManager.Verify(m => m.AddDataPoint(It.IsAny<string>(), It.IsAny<EwsDataPoint>()), Times.AtLeast(7)); // 7 data points for Tokyo
        }

        [Test]
        public void Execute_WithEmptyCities_CompletesSuccessfully()
        {
            // Arrange
            _processor.Initialize(_mockContext.Object);
            _processor.Cities = "";
            
            _mockEwsServerManager.Setup(m => m.ServerExists(It.IsAny<string>()))
                                .Returns(false);

            // Act
            var result = _processor.Execute();

            // Assert
            Assert.IsTrue(result.Success);
            _mockEwsServerManager.Verify(m => m.AddDataPoint(It.IsAny<string>(), It.IsAny<EwsDataPoint>()), Times.Never);
        }

        [Test]
        public void Execute_WithException_ReturnsFailureResult()
        {
            // Arrange
            _processor.Initialize(_mockContext.Object);
            
            _mockEwsServerManager.Setup(m => m.ServerExists(It.IsAny<string>()))
                                .Throws(new Exception("Test exception"));

            // Act
            var result = _processor.Execute();

            // Assert
            Assert.IsFalse(result.Success);
            Assert.That(result.Message, Does.Contain("Setup failed"));
            _mockLogger.Verify(l => l.Error(It.IsAny<string>(), It.IsAny<Exception>()), Times.Once);
        }

        [Test]
        public void Cities_WithCommaAndSpaces_ParsesCorrectly()
        {
            // Arrange
            _processor.Initialize(_mockContext.Object);
            _processor.Cities = " London , New York , Tokyo ";
            
            _mockEwsServerManager.Setup(m => m.ServerExists(It.IsAny<string>()))
                                .Returns(false);

            // Act
            var result = _processor.Execute();

            // Assert
            Assert.IsTrue(result.Success);
            
            // Verify data points were created for all three cities
            _mockEwsServerManager.Verify(m => m.AddDataPoint(It.IsAny<string>(), 
                It.Is<EwsDataPoint>(dp => dp.Name.StartsWith("London_"))), Times.AtLeast(7));
            _mockEwsServerManager.Verify(m => m.AddDataPoint(It.IsAny<string>(), 
                It.Is<EwsDataPoint>(dp => dp.Name.StartsWith("New_York_"))), Times.AtLeast(7));
            _mockEwsServerManager.Verify(m => m.AddDataPoint(It.IsAny<string>(), 
                It.Is<EwsDataPoint>(dp => dp.Name.StartsWith("Tokyo_"))), Times.AtLeast(7));
        }

        [Test]
        public void DefaultProperties_HaveExpectedValues()
        {
            // Assert
            Assert.That(_processor.ServerName, Is.EqualTo("WeatherServer"));
            Assert.That(_processor.Cities, Is.EqualTo("London,New York,Tokyo"));
            Assert.That(_processor.Description, Is.EqualTo("Weather data from OpenWeatherMap API"));
        }
    }
}
