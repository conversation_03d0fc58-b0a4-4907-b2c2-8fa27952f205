<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net48</TargetFramework>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.5.0" />
    <PackageReference Include="NUnit" Version="3.13.3" />
    <PackageReference Include="NUnit3TestAdapter" Version="4.4.2" />
    <PackageReference Include="NUnit.Analyzers" Version="3.6.1" />
    <PackageReference Include="coverlet.collector" Version="3.2.0" />
    <PackageReference Include="Moq" Version="4.20.69" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\WeatherExtension\WeatherExtension.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="SmartConnector.Core">
      <HintPath>..\..\SmartConnector\SmartConnector.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="SmartConnector.Common">
      <HintPath>..\..\SmartConnector\SmartConnector.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="SmartConnector.Ews">
      <HintPath>..\..\SmartConnector\SmartConnector.Ews.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>

</Project>
