using System;
using System.Collections.Generic;
using SmartConnector.Core;
using SmartConnector.Core.Attributes;
using SmartConnector.Core.Interfaces;
using SmartConnector.Ews;
using SmartConnector.Ews.Interfaces;

namespace WeatherExtension.Processors
{
    /// <summary>
    /// Setup processor that creates and provisions the EWS server data structure for weather data
    /// </summary>
    [Processor("WeatherSetup", "Sets up weather data EWS server structure")]
    public class WeatherSetupProcessor : IProcessor
    {
        private IEwsServerManager _ewsServerManager;
        private ILogger _logger;

        [ProcessorValue("ServerName", "Name of the EWS server to create", "WeatherServer")]
        public string ServerName { get; set; } = "WeatherServer";

        [ProcessorValue("Cities", "Comma-separated list of cities to monitor", "London,New York,Tokyo")]
        public string Cities { get; set; } = "London,New York,Tokyo";

        [ProcessorValue("Description", "Description of the weather server", "Weather data from OpenWeatherMap API")]
        public string Description { get; set; } = "Weather data from OpenWeatherMap API";

        public void Initialize(IProcessorContext context)
        {
            _ewsServerManager = context.GetService<IEwsServerManager>();
            _logger = context.GetService<ILogger>();
            
            if (_ewsServerManager == null)
                throw new InvalidOperationException("EWS Server Manager service not available");
            
            if (_logger == null)
                throw new InvalidOperationException("Logger service not available");
        }

        public ProcessorResult Execute()
        {
            try
            {
                _logger.Info($"Setting up weather EWS server: {ServerName}");

                // Check if server already exists
                if (_ewsServerManager.ServerExists(ServerName))
                {
                    _logger.Info($"EWS server {ServerName} already exists, updating structure");
                }
                else
                {
                    // Create the EWS server
                    var serverConfig = new EwsServerConfiguration
                    {
                        Name = ServerName,
                        Description = Description,
                        ServerType = typeof(WeatherEwsServer).FullName
                    };

                    _ewsServerManager.CreateServer(serverConfig);
                    _logger.Info($"Created EWS server: {ServerName}");
                }

                // Set up data structure for each city
                var cityList = ParseCities(Cities);
                foreach (var city in cityList)
                {
                    SetupCityDataStructure(city);
                }

                _logger.Info($"Weather setup completed successfully for {cityList.Count} cities");
                return ProcessorResult.Success("Weather EWS server setup completed successfully");
            }
            catch (Exception ex)
            {
                _logger.Error($"Error setting up weather EWS server: {ex.Message}", ex);
                return ProcessorResult.Failure($"Setup failed: {ex.Message}");
            }
        }

        private List<string> ParseCities(string citiesString)
        {
            var cities = new List<string>();
            if (!string.IsNullOrWhiteSpace(citiesString))
            {
                var cityArray = citiesString.Split(',');
                foreach (var city in cityArray)
                {
                    var trimmedCity = city.Trim();
                    if (!string.IsNullOrEmpty(trimmedCity))
                    {
                        cities.Add(trimmedCity);
                    }
                }
            }
            return cities;
        }

        private void SetupCityDataStructure(string city)
        {
            var cityKey = city.Replace(" ", "_").Replace(",", "");
            
            // Create data points for the city
            var dataPoints = new List<EwsDataPoint>
            {
                new EwsDataPoint($"{cityKey}_Temperature", "Temperature", "°C", EwsDataType.Float),
                new EwsDataPoint($"{cityKey}_Humidity", "Humidity", "%", EwsDataType.Integer),
                new EwsDataPoint($"{cityKey}_Pressure", "Pressure", "hPa", EwsDataType.Float),
                new EwsDataPoint($"{cityKey}_WindSpeed", "Wind Speed", "m/s", EwsDataType.Float),
                new EwsDataPoint($"{cityKey}_WindDirection", "Wind Direction", "°", EwsDataType.Integer),
                new EwsDataPoint($"{cityKey}_Description", "Weather Description", "", EwsDataType.String),
                new EwsDataPoint($"{cityKey}_LastUpdated", "Last Updated", "", EwsDataType.DateTime)
            };

            // Add data points to the server
            foreach (var dataPoint in dataPoints)
            {
                _ewsServerManager.AddDataPoint(ServerName, dataPoint);
            }

            _logger.Info($"Set up data structure for city: {city}");
        }

        public void Dispose()
        {
            // No resources to dispose
        }
    }
}
