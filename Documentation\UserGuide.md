# Weather Extension User Guide

## Overview

The Weather Extension for SmartConnector provides real-time weather data from the OpenWeatherMap API through EWS (Enterprise Web Services). This guide covers installation, configuration, and usage of the extension.

## Prerequisites

- SmartConnector 2.0.0 or higher installed and running
- OpenWeatherMap API key (free registration at https://openweathermap.org/api)
- Administrative access to SmartConnector Portal

## Installation

### Step 1: Obtain API Key

1. Visit https://openweathermap.org/api
2. Create a free account
3. Generate an API key
4. Note the API key for later configuration

### Step 2: Deploy Extension

#### Option A: Using Deployment Script

```powershell
# Run from PowerShell as Administrator
.\Scripts\Deploy.ps1 -SmartConnectorPath "C:\Program Files\SmartConnector" -RestartService
```

#### Option B: Manual Deployment

1. Copy `WeatherExtension.dll` to `[SmartConnector]\Extensions\WeatherExtension\`
2. Copy configuration files to `[SmartConnector]\Extensions\WeatherExtension\Configuration\`
3. Restart SmartConnector service

### Step 3: Import Configurations

1. Open SmartConnector Portal (typically http://localhost:8080/portal)
2. Navigate to **Configuration** > **Processors**
3. Click **Import Configuration**
4. Import `WeatherSetupConfig.json`
5. Import `WeatherUpdateConfig.json`

## Configuration

### WeatherSetup Processor

This processor creates the EWS server structure and must be run first.

**Configuration Parameters:**

| Parameter | Description | Default Value |
|-----------|-------------|---------------|
| ServerName | Name of the EWS server | WeatherServer |
| Cities | Comma-separated list of cities | London,New York,Tokyo |
| Description | Server description | Weather data from OpenWeatherMap API |

**Example Configuration:**
```json
{
  "ServerName": "WeatherServer",
  "Cities": "London,Paris,New York,Tokyo,Sydney",
  "Description": "Real-time weather data"
}
```

### WeatherUpdate Processor

This processor retrieves weather data and updates the EWS server.

**Configuration Parameters:**

| Parameter | Description | Default Value | Required |
|-----------|-------------|---------------|----------|
| ApiKey | OpenWeatherMap API key | (empty) | Yes |
| ServerName | EWS server name | WeatherServer | No |
| Cities | Cities to monitor | London,New York,Tokyo | No |
| Units | Temperature units | metric | No |
| UpdateIntervalMinutes | Update frequency | 15 | No |

**Temperature Units:**
- `metric`: Celsius, m/s, hPa
- `imperial`: Fahrenheit, mph, hPa  
- `kelvin`: Kelvin, m/s, hPa

**Example Configuration:**
```json
{
  "ApiKey": "your-openweathermap-api-key-here",
  "ServerName": "WeatherServer",
  "Cities": "London,Paris,New York,Tokyo,Sydney",
  "Units": "metric",
  "UpdateIntervalMinutes": 10
}
```

## Setup Process

### 1. Configure WeatherSetup Processor

1. In SmartConnector Portal, go to **Processors** > **WeatherSetup**
2. Set desired cities in the **Cities** parameter
3. Optionally modify **ServerName** and **Description**
4. Click **Save Configuration**

### 2. Run WeatherSetup Processor

1. Click **Run Now** on the WeatherSetup processor
2. Verify successful execution in the processor logs
3. Check that the EWS server was created

### 3. Configure WeatherUpdate Processor

1. Go to **Processors** > **WeatherUpdate**
2. **Important**: Set your OpenWeatherMap API key in the **ApiKey** parameter
3. Ensure **Cities** matches the WeatherSetup configuration
4. Set desired **UpdateIntervalMinutes** (minimum 5 minutes recommended)
5. Click **Save Configuration**

### 4. Enable WeatherUpdate Processor

1. Set the processor **Schedule** to **Interval**
2. Set **Interval** to match **UpdateIntervalMinutes**
3. Enable the processor
4. Verify data updates in the processor logs

## Data Points

For each configured city, the following data points are available:

| Data Point | Description | Units | Type |
|------------|-------------|-------|------|
| {City}_Temperature | Current temperature | °C/°F/K | Float |
| {City}_Humidity | Relative humidity | % | Integer |
| {City}_Pressure | Atmospheric pressure | hPa | Float |
| {City}_WindSpeed | Wind speed | m/s or mph | Float |
| {City}_WindDirection | Wind direction | degrees | Integer |
| {City}_Description | Weather condition | text | String |
| {City}_LastUpdated | Last update time | timestamp | DateTime |

**Note**: City names with spaces are converted to underscores (e.g., "New York" becomes "New_York").

## EWS Client Usage

### Accessing the EWS Server

The weather data is available through the EWS server at:
- **WSDL**: `http://[server]:8080/WeatherServer?wsdl`
- **Endpoint**: `http://[server]:8080/WeatherServer`

### SOAP Request Examples

#### GetValues Request

```xml
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <GetValues xmlns="http://schemas.schneider-electric.com/smartconnector/ews">
      <Items>
        <Item><ItemName>London_Temperature</ItemName></Item>
        <Item><ItemName>London_Humidity</ItemName></Item>
        <Item><ItemName>Paris_WindSpeed</ItemName></Item>
      </Items>
    </GetValues>
  </soap:Body>
</soap:Envelope>
```

#### GetItemList Request

```xml
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <GetItemList xmlns="http://schemas.schneider-electric.com/smartconnector/ews">
    </GetItemList>
  </soap:Body>
</soap:Envelope>
```

### Testing with SoapUI

1. Create new SOAP project in SoapUI
2. Use WSDL: `http://localhost:8080/WeatherServer?wsdl`
3. Test GetValues and GetItemList operations
4. Verify data quality is "Good" and values are current

## Monitoring and Troubleshooting

### Processor Logs

Monitor processor execution in SmartConnector Portal:
1. Go to **Monitoring** > **Processor Logs**
2. Filter by processor name (WeatherSetup or WeatherUpdate)
3. Check for errors or warnings

### Common Issues

#### "API key is required" Error
- **Cause**: OpenWeatherMap API key not configured
- **Solution**: Set ApiKey parameter in WeatherUpdate processor

#### "EWS server not found" Error  
- **Cause**: WeatherSetup processor not run successfully
- **Solution**: Run WeatherSetup processor first

#### "Failed to retrieve weather data" Error
- **Cause**: Network connectivity or invalid API key
- **Solution**: Check internet connection and verify API key

#### No Data Updates
- **Cause**: WeatherUpdate processor not enabled or API rate limits
- **Solution**: Enable processor and check update interval

### Log Locations

SmartConnector logs are typically located at:
- Windows: `%ProgramData%\SmartConnector\Logs\`
- Filter by: `WeatherExtension`

## Best Practices

### API Usage
- Use reasonable update intervals (15+ minutes recommended)
- Monitor API usage to stay within rate limits
- Free OpenWeatherMap accounts have 1000 calls/day limit

### Performance
- Limit number of cities to reasonable amount (5-10 recommended)
- Consider API response time when setting update intervals
- Monitor SmartConnector performance impact

### Security
- Protect API key configuration
- Use strong-named assemblies in production
- Regularly update to latest extension version

## Advanced Configuration

### Custom City Lists
Cities can be specified in various formats:
- City name: `London`
- City, Country: `London,UK`
- City, State, Country: `New York,NY,US`

### Multiple Server Instances
You can create multiple weather servers for different regions:
1. Run WeatherSetup with different ServerName
2. Configure separate WeatherUpdate processors
3. Each server will have independent data points

### Integration with Other Systems
The EWS interface allows integration with:
- SCADA systems
- Building management systems
- Custom applications
- Reporting tools

## Support

For technical support:
- Check SmartConnector documentation
- Review processor logs for detailed error information
- Verify OpenWeatherMap API status
- Contact system administrator for SmartConnector issues
