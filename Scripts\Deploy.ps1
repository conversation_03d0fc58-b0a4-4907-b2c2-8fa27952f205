# SmartConnector Weather Extension Deployment Script
# This script deploys the Weather Extension to a SmartConnector installation

param(
    [Parameter(Mandatory=$true)]
    [string]$SmartConnectorPath,
    
    [Parameter(Mandatory=$false)]
    [string]$BuildConfiguration = "Release",
    
    [Parameter(Mandatory=$false)]
    [switch]$RestartService = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$Force = $false
)

# Script configuration
$ExtensionName = "WeatherExtension"
$ServiceName = "SmartConnector"

Write-Host "=== SmartConnector Weather Extension Deployment ===" -ForegroundColor Green
Write-Host "Target Path: $SmartConnectorPath" -ForegroundColor Yellow
Write-Host "Build Configuration: $BuildConfiguration" -ForegroundColor Yellow

# Validate SmartConnector installation
if (-not (Test-Path $SmartConnectorPath)) {
    Write-Error "SmartConnector path not found: $SmartConnectorPath"
    exit 1
}

$ExtensionsPath = Join-Path $SmartConnectorPath "Extensions"
if (-not (Test-Path $ExtensionsPath)) {
    Write-Host "Creating Extensions directory: $ExtensionsPath" -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $ExtensionsPath -Force | Out-Null
}

# Validate build output
$BuildPath = Join-Path $PSScriptRoot "..\WeatherExtension\bin\$BuildConfiguration"
$MainAssembly = Join-Path $BuildPath "$ExtensionName.dll"

if (-not (Test-Path $MainAssembly)) {
    Write-Error "Build output not found: $MainAssembly"
    Write-Host "Please build the solution first using: dotnet build --configuration $BuildConfiguration" -ForegroundColor Yellow
    exit 1
}

# Check if service is running
$Service = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
if ($Service -and $Service.Status -eq "Running") {
    if ($RestartService) {
        Write-Host "Stopping SmartConnector service..." -ForegroundColor Yellow
        Stop-Service -Name $ServiceName -Force
        Start-Sleep -Seconds 5
    } else {
        Write-Warning "SmartConnector service is running. Use -RestartService to automatically restart it."
        if (-not $Force) {
            $response = Read-Host "Continue deployment? (y/N)"
            if ($response -ne "y" -and $response -ne "Y") {
                Write-Host "Deployment cancelled." -ForegroundColor Red
                exit 0
            }
        }
    }
}

try {
    # Create extension directory
    $ExtensionPath = Join-Path $ExtensionsPath $ExtensionName
    if (Test-Path $ExtensionPath) {
        Write-Host "Removing existing extension..." -ForegroundColor Yellow
        Remove-Item -Path $ExtensionPath -Recurse -Force
    }
    
    New-Item -ItemType Directory -Path $ExtensionPath -Force | Out-Null
    Write-Host "Created extension directory: $ExtensionPath" -ForegroundColor Green

    # Copy main assembly
    Copy-Item -Path $MainAssembly -Destination $ExtensionPath -Force
    Write-Host "Deployed: $ExtensionName.dll" -ForegroundColor Green

    # Copy dependencies (if any)
    $Dependencies = @("Newtonsoft.Json.dll")
    foreach ($Dependency in $Dependencies) {
        $DepPath = Join-Path $BuildPath $Dependency
        if (Test-Path $DepPath) {
            Copy-Item -Path $DepPath -Destination $ExtensionPath -Force
            Write-Host "Deployed dependency: $Dependency" -ForegroundColor Green
        }
    }

    # Copy configuration files
    $ConfigPath = Join-Path $ExtensionPath "Configuration"
    New-Item -ItemType Directory -Path $ConfigPath -Force | Out-Null
    
    $SourceConfigPath = Join-Path $PSScriptRoot "..\WeatherExtension\Configuration"
    if (Test-Path $SourceConfigPath) {
        Copy-Item -Path "$SourceConfigPath\*" -Destination $ConfigPath -Recurse -Force
        Write-Host "Deployed configuration files" -ForegroundColor Green
    }

    # Copy documentation
    $DocsPath = Join-Path $ExtensionPath "Documentation"
    New-Item -ItemType Directory -Path $DocsPath -Force | Out-Null
    
    $ReadmePath = Join-Path $PSScriptRoot "..\README.md"
    if (Test-Path $ReadmePath) {
        Copy-Item -Path $ReadmePath -Destination $DocsPath -Force
        Write-Host "Deployed documentation" -ForegroundColor Green
    }

    Write-Host "=== Deployment Completed Successfully ===" -ForegroundColor Green

    # Restart service if requested
    if ($RestartService -and $Service) {
        Write-Host "Starting SmartConnector service..." -ForegroundColor Yellow
        Start-Service -Name $ServiceName
        Start-Sleep -Seconds 10
        
        $Service = Get-Service -Name $ServiceName
        if ($Service.Status -eq "Running") {
            Write-Host "SmartConnector service started successfully" -ForegroundColor Green
        } else {
            Write-Warning "SmartConnector service failed to start. Check service logs."
        }
    }

    Write-Host ""
    Write-Host "Next Steps:" -ForegroundColor Cyan
    Write-Host "1. Open SmartConnector Portal" -ForegroundColor White
    Write-Host "2. Import processor configurations from: $ConfigPath" -ForegroundColor White
    Write-Host "3. Configure OpenWeatherMap API key in WeatherUpdate processor" -ForegroundColor White
    Write-Host "4. Run WeatherSetup processor first" -ForegroundColor White
    Write-Host "5. Enable WeatherUpdate processor for automatic updates" -ForegroundColor White

} catch {
    Write-Error "Deployment failed: $($_.Exception.Message)"
    exit 1
}
