<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net48</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <SignAssembly>true</SignAssembly>
    <AssemblyOriginatorKeyFile>WeatherExtension.snk</AssemblyOriginatorKeyFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="SmartConnector.Core">
      <HintPath>..\..\SmartConnector\SmartConnector.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="SmartConnector.Common">
      <HintPath>..\..\SmartConnector\SmartConnector.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="SmartConnector.Ews">
      <HintPath>..\..\SmartConnector\SmartConnector.Ews.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="SmartConnector.Licensing">
      <HintPath>..\..\SmartConnector\SmartConnector.Licensing.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>

</Project>
