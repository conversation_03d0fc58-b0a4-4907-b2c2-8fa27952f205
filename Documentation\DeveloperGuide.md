# Weather Extension Developer Guide

## Architecture Overview

The Weather Extension demonstrates a complete SmartConnector extension implementation following the official developer guide patterns. It consists of three main components:

### Core Components

1. **SetupProcessor** (`WeatherSetupProcessor`)
   - Creates EWS server infrastructure
   - Provisions data points for each city
   - Runs once during initial setup

2. **UpdateProcessor** (`WeatherUpdateProcessor`)
   - Retrieves data from OpenWeatherMap API
   - Updates EWS data points asynchronously
   - Runs on scheduled intervals

3. **EWS Server** (`WeatherEwsServer`)
   - Serves data to EWS clients
   - Handles GetValues, SetValues, and GetItemList requests
   - Maintains in-memory data cache

### Supporting Components

- **WeatherService**: HTTP client for OpenWeatherMap API
- **WeatherData Models**: Data transfer objects for API responses
- **LicenseValidator**: SmartConnector licensing integration
- **Configuration**: JSON-based processor configurations

## Development Environment Setup

### Prerequisites

- Visual Studio 2019 or higher
- .NET Framework 4.8 SDK
- SmartConnector SDK assemblies
- NuGet package manager

### Project Structure

```
WeatherExtension/
├── Models/                    # Data models
│   └── WeatherData.cs        # API response models
├── Services/                  # External services
│   └── WeatherService.cs     # OpenWeatherMap API client
├── Processors/                # SmartConnector processors
│   ├── WeatherSetupProcessor.cs
│   └── WeatherUpdateProcessor.cs
├── Servers/                   # EWS server implementations
│   └── WeatherEwsServer.cs
├── Licensing/                 # License validation
│   └── WeatherLicenseValidator.cs
├── Configuration/             # Processor configurations
│   ├── WeatherSetupConfig.json
│   ├── WeatherUpdateConfig.json
│   └── ExtensionManifest.xml
└── Properties/
    └── AssemblyInfo.cs

WeatherExtension.Tests/        # Unit tests
├── Services/
├── Processors/
└── Servers/
```

### Dependencies

#### NuGet Packages
- `Newtonsoft.Json` (13.0.3): JSON serialization
- `System.Net.Http` (4.3.4): HTTP client functionality

#### SmartConnector References
- `SmartConnector.Core.dll`: Core interfaces and attributes
- `SmartConnector.Common.dll`: Common utilities
- `SmartConnector.Ews.dll`: EWS server framework
- `SmartConnector.Licensing.dll`: Licensing support

## Implementation Details

### Processor Development

#### Processor Attributes

```csharp
[Processor("WeatherSetup", "Sets up weather data EWS server structure")]
public class WeatherSetupProcessor : IProcessor
```

#### Processor Values

```csharp
[ProcessorValue("ServerName", "Name of the EWS server to create", "WeatherServer")]
public string ServerName { get; set; } = "WeatherServer";
```

#### Initialization Pattern

```csharp
public void Initialize(IProcessorContext context)
{
    _ewsServerManager = context.GetService<IEwsServerManager>();
    _logger = context.GetService<ILogger>();
    
    // Validate required services
    if (_ewsServerManager == null)
        throw new InvalidOperationException("EWS Server Manager service not available");
}
```

#### Execution Pattern

```csharp
public ProcessorResult Execute()
{
    try
    {
        // Processor logic here
        return ProcessorResult.Success("Operation completed successfully");
    }
    catch (Exception ex)
    {
        _logger.Error($"Error: {ex.Message}", ex);
        return ProcessorResult.Failure($"Operation failed: {ex.Message}");
    }
}
```

### EWS Server Development

#### Server Base Class

```csharp
public class WeatherEwsServer : EwsServerBase
{
    public override void Initialize(IEwsServerContext context)
    {
        base.Initialize(context);
        // Custom initialization
    }
}
```

#### Request Handling

```csharp
public override EwsResponse GetValues(EwsRequest request)
{
    var response = new EwsResponse { Items = new List<EwsResponseItem>() };
    
    foreach (var requestItem in request.Items)
    {
        var responseItem = GetDataPointValue(requestItem.ItemName);
        response.Items.Add(responseItem);
    }
    
    return response;
}
```

#### Thread Safety

```csharp
private readonly object _dataLock = new object();

public void UpdateDataPoint(string name, object value, DateTime timestamp)
{
    lock (_dataLock)
    {
        if (_dataPoints.TryGetValue(name, out var dataPoint))
        {
            dataPoint.Value = value;
            dataPoint.LastUpdated = timestamp;
        }
    }
}
```

### Asynchronous Operations

#### HTTP Client Usage

```csharp
public async Task<WeatherData> GetWeatherAsync(string city, string units = "metric")
{
    var url = $"{_baseUrl}?q={Uri.EscapeDataString(city)}&appid={_apiKey}&units={units}";
    
    var response = await _httpClient.GetAsync(url);
    response.EnsureSuccessStatusCode();
    
    var json = await response.Content.ReadAsStringAsync();
    return JsonConvert.DeserializeObject<WeatherData>(json);
}
```

#### Task Coordination

```csharp
var updateTasks = new List<Task>();
foreach (var city in cityList)
{
    updateTasks.Add(UpdateCityWeatherAsync(city));
}

Task.WaitAll(updateTasks.ToArray(), TimeSpan.FromMinutes(2));
```

## Testing Strategy

### Unit Testing Framework

- **NUnit**: Primary testing framework
- **Moq**: Mocking framework for dependencies
- **Test Categories**: Unit, Integration, Performance

### Test Structure

```csharp
[TestFixture]
public class WeatherServiceTests
{
    private WeatherService _weatherService;
    
    [SetUp]
    public void SetUp()
    {
        _weatherService = new WeatherService("test-api-key");
    }
    
    [TearDown]
    public void TearDown()
    {
        _weatherService?.Dispose();
    }
    
    [Test]
    public void Constructor_WithNullApiKey_ThrowsArgumentNullException()
    {
        Assert.Throws<ArgumentNullException>(() => new WeatherService(null));
    }
}
```

### Mocking SmartConnector Services

```csharp
var mockContext = new Mock<IProcessorContext>();
var mockEwsServerManager = new Mock<IEwsServerManager>();
var mockLogger = new Mock<ILogger>();

mockContext.Setup(c => c.GetService<IEwsServerManager>())
          .Returns(mockEwsServerManager.Object);
```

### Integration Testing

```csharp
[Test, Explicit("Integration test requiring valid API key")]
public async Task GetWeatherAsync_WithValidApiKeyAndCity_ReturnsWeatherData()
{
    const string validApiKey = "your-valid-api-key-here";
    using var service = new WeatherService(validApiKey);
    
    var result = await service.GetWeatherAsync("London");
    
    Assert.IsNotNull(result);
    Assert.That(result.Location, Is.EqualTo("London"));
}
```

## Error Handling and Logging

### Exception Handling Patterns

```csharp
try
{
    var weatherData = await _weatherService.GetWeatherAsync(city, Units);
    UpdateEwsDataPoints(city, weatherData);
}
catch (HttpRequestException ex)
{
    _logger.Error($"Network error for {city}: {ex.Message}", ex);
}
catch (JsonException ex)
{
    _logger.Error($"JSON parsing error for {city}: {ex.Message}", ex);
}
catch (Exception ex)
{
    _logger.Error($"Unexpected error for {city}: {ex.Message}", ex);
}
```

### Logging Levels

- **Debug**: Detailed execution flow
- **Info**: Normal operation events
- **Warning**: Recoverable issues
- **Error**: Unrecoverable errors with exceptions

## Configuration Management

### Processor Configuration Schema

```json
{
  "ProcessorConfiguration": {
    "Name": "WeatherUpdate",
    "ProcessorType": "WeatherExtension.Processors.WeatherUpdateProcessor",
    "Assembly": "WeatherExtension.dll",
    "Values": {
      "ApiKey": {
        "Value": "",
        "Required": true,
        "Sensitive": true
      }
    },
    "Schedule": {
      "Type": "Interval",
      "IntervalMinutes": 15
    }
  }
}
```

### Extension Manifest

```xml
<ExtensionManifest>
  <Extension>
    <Name>WeatherExtension</Name>
    <Version>1.0.0</Version>
    <Processors>
      <Processor>
        <Name>WeatherSetup</Name>
        <Type>WeatherExtension.Processors.WeatherSetupProcessor</Type>
      </Processor>
    </Processors>
  </Extension>
</ExtensionManifest>
```

## Licensing Integration

### License Validation

```csharp
public bool ValidateLicense()
{
    var licenseManager = LicenseManager.Instance;
    var licenseInfo = licenseManager.GetLicense(_extensionId);
    
    if (licenseInfo?.IsValid != true)
        return false;
        
    if (licenseInfo.ExpirationDate.HasValue && 
        licenseInfo.ExpirationDate.Value < DateTime.UtcNow)
        return false;
        
    return true;
}
```

### License Enforcement

```csharp
public void Initialize(IProcessorContext context)
{
    var licenseValidator = new WeatherLicenseValidator(_logger);
    licenseValidator.ValidateLicenseOrThrow();
    
    // Continue with initialization
}
```

## Deployment and Distribution

### Build Process

1. **Restore NuGet packages**: `dotnet restore`
2. **Build solution**: `dotnet build --configuration Release`
3. **Run tests**: `dotnet test`
4. **Generate strong name**: `sn -k WeatherExtension.snk`

### Deployment Structure

```
SmartConnector/Extensions/WeatherExtension/
├── WeatherExtension.dll
├── Newtonsoft.Json.dll
├── Configuration/
│   ├── WeatherSetupConfig.json
│   ├── WeatherUpdateConfig.json
│   └── ExtensionManifest.xml
└── Documentation/
    ├── UserGuide.md
    └── README.md
```

### Automated Deployment

```powershell
.\Scripts\Deploy.ps1 -SmartConnectorPath "C:\Program Files\SmartConnector" -RestartService
```

## Performance Considerations

### Memory Management

- Dispose HTTP clients properly
- Use object pooling for frequent allocations
- Monitor memory usage in long-running processors

### API Rate Limiting

- Respect OpenWeatherMap rate limits (1000 calls/day for free tier)
- Implement exponential backoff for failed requests
- Cache responses when appropriate

### Concurrency

- Use thread-safe collections for shared data
- Implement proper locking for EWS server data updates
- Consider async/await patterns for I/O operations

## Extending the Extension

### Adding New Data Sources

1. Create new service class (e.g., `AnotherWeatherService`)
2. Add corresponding data models
3. Update processors to use new service
4. Add configuration parameters
5. Update unit tests

### Adding New Data Points

1. Modify `SetupCityDataStructure()` method
2. Update `UpdateEwsDataPoints()` method
3. Add corresponding unit tests
4. Update documentation

### Custom EWS Operations

```csharp
public override EwsResponse CustomOperation(EwsRequest request)
{
    // Implement custom EWS operation
    return new EwsResponse();
}
```

## Debugging and Troubleshooting

### Visual Studio Debugging

1. Set SmartConnector as startup project
2. Configure debugging symbols
3. Set breakpoints in extension code
4. Use "Attach to Process" for running service

### Log Analysis

- Use structured logging with correlation IDs
- Implement log filtering by component
- Monitor performance metrics
- Set up log aggregation for production

### Common Issues

- **Assembly loading**: Check assembly references and versions
- **Service dependencies**: Verify SmartConnector services are available
- **Configuration**: Validate JSON configuration syntax
- **Licensing**: Ensure valid license is installed

This developer guide provides the foundation for understanding, extending, and maintaining the Weather Extension codebase.
