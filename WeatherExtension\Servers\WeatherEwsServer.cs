using System;
using System.Collections.Generic;
using System.Linq;
using SmartConnector.Ews;
using SmartConnector.Ews.Interfaces;
using SmartConnector.Core.Interfaces;

namespace WeatherExtension.Servers
{
    /// <summary>
    /// Custom EWS server for serving weather data to EWS clients
    /// </summary>
    public class WeatherEwsServer : EwsServerBase
    {
        private ILogger _logger;
        private readonly Dictionary<string, EwsDataPoint> _dataPoints;
        private readonly object _dataLock = new object();

        public WeatherEwsServer() : base()
        {
            _dataPoints = new Dictionary<string, EwsDataPoint>();
        }

        public override void Initialize(IEwsServerContext context)
        {
            base.Initialize(context);
            _logger = context.GetService<ILogger>();
            
            if (_logger == null)
                throw new InvalidOperationException("Logger service not available");

            _logger.Info($"Initializing Weather EWS Server: {ServerName}");
        }

        public override EwsResponse GetValues(EwsRequest request)
        {
            try
            {
                _logger.Debug($"GetValues request received with {request.Items?.Count ?? 0} items");

                var response = new EwsResponse
                {
                    Items = new List<EwsResponseItem>()
                };

                if (request.Items != null)
                {
                    foreach (var requestItem in request.Items)
                    {
                        var responseItem = GetDataPointValue(requestItem.ItemName);
                        response.Items.Add(responseItem);
                    }
                }

                _logger.Debug($"GetValues response prepared with {response.Items.Count} items");
                return response;
            }
            catch (Exception ex)
            {
                _logger.Error($"Error processing GetValues request: {ex.Message}", ex);
                return CreateErrorResponse("Internal server error");
            }
        }

        public override EwsResponse SetValues(EwsRequest request)
        {
            try
            {
                _logger.Debug($"SetValues request received with {request.Items?.Count ?? 0} items");

                var response = new EwsResponse
                {
                    Items = new List<EwsResponseItem>()
                };

                if (request.Items != null)
                {
                    foreach (var requestItem in request.Items)
                    {
                        var responseItem = SetDataPointValue(requestItem);
                        response.Items.Add(responseItem);
                    }
                }

                _logger.Debug($"SetValues response prepared with {response.Items.Count} items");
                return response;
            }
            catch (Exception ex)
            {
                _logger.Error($"Error processing SetValues request: {ex.Message}", ex);
                return CreateErrorResponse("Internal server error");
            }
        }

        public override EwsResponse GetItemList(EwsRequest request)
        {
            try
            {
                _logger.Debug("GetItemList request received");

                var response = new EwsResponse
                {
                    Items = new List<EwsResponseItem>()
                };

                lock (_dataLock)
                {
                    foreach (var dataPoint in _dataPoints.Values)
                    {
                        var responseItem = new EwsResponseItem
                        {
                            ItemName = dataPoint.Name,
                            Value = dataPoint.Value,
                            Quality = EwsQuality.Good,
                            Timestamp = dataPoint.LastUpdated,
                            DataType = dataPoint.DataType,
                            Units = dataPoint.Units,
                            Description = dataPoint.Description
                        };

                        response.Items.Add(responseItem);
                    }
                }

                _logger.Debug($"GetItemList response prepared with {response.Items.Count} items");
                return response;
            }
            catch (Exception ex)
            {
                _logger.Error($"Error processing GetItemList request: {ex.Message}", ex);
                return CreateErrorResponse("Internal server error");
            }
        }

        private EwsResponseItem GetDataPointValue(string itemName)
        {
            lock (_dataLock)
            {
                if (_dataPoints.TryGetValue(itemName, out var dataPoint))
                {
                    return new EwsResponseItem
                    {
                        ItemName = itemName,
                        Value = dataPoint.Value,
                        Quality = EwsQuality.Good,
                        Timestamp = dataPoint.LastUpdated,
                        DataType = dataPoint.DataType,
                        Units = dataPoint.Units
                    };
                }
                else
                {
                    _logger.Warning($"Data point not found: {itemName}");
                    return new EwsResponseItem
                    {
                        ItemName = itemName,
                        Quality = EwsQuality.Bad,
                        ErrorMessage = "Item not found"
                    };
                }
            }
        }

        private EwsResponseItem SetDataPointValue(EwsRequestItem requestItem)
        {
            try
            {
                lock (_dataLock)
                {
                    if (_dataPoints.TryGetValue(requestItem.ItemName, out var dataPoint))
                    {
                        // Weather data is read-only, so reject set operations
                        _logger.Warning($"Attempt to set read-only weather data point: {requestItem.ItemName}");
                        return new EwsResponseItem
                        {
                            ItemName = requestItem.ItemName,
                            Quality = EwsQuality.Bad,
                            ErrorMessage = "Weather data is read-only"
                        };
                    }
                    else
                    {
                        _logger.Warning($"Data point not found for set operation: {requestItem.ItemName}");
                        return new EwsResponseItem
                        {
                            ItemName = requestItem.ItemName,
                            Quality = EwsQuality.Bad,
                            ErrorMessage = "Item not found"
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"Error setting data point {requestItem.ItemName}: {ex.Message}", ex);
                return new EwsResponseItem
                {
                    ItemName = requestItem.ItemName,
                    Quality = EwsQuality.Bad,
                    ErrorMessage = "Internal error"
                };
            }
        }

        /// <summary>
        /// Updates a data point value (called by the update processor)
        /// </summary>
        public void UpdateDataPoint(string name, object value, DateTime timestamp)
        {
            lock (_dataLock)
            {
                if (_dataPoints.TryGetValue(name, out var dataPoint))
                {
                    dataPoint.Value = value;
                    dataPoint.LastUpdated = timestamp;
                    _logger.Debug($"Updated data point {name} with value {value}");
                }
                else
                {
                    _logger.Warning($"Attempted to update non-existent data point: {name}");
                }
            }
        }

        /// <summary>
        /// Adds a new data point to the server
        /// </summary>
        public void AddDataPoint(EwsDataPoint dataPoint)
        {
            lock (_dataLock)
            {
                _dataPoints[dataPoint.Name] = dataPoint;
                _logger.Debug($"Added data point: {dataPoint.Name}");
            }
        }

        private EwsResponse CreateErrorResponse(string errorMessage)
        {
            return new EwsResponse
            {
                Items = new List<EwsResponseItem>
                {
                    new EwsResponseItem
                    {
                        Quality = EwsQuality.Bad,
                        ErrorMessage = errorMessage
                    }
                }
            };
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _logger?.Info($"Disposing Weather EWS Server: {ServerName}");
                lock (_dataLock)
                {
                    _dataPoints.Clear();
                }
            }
            base.Dispose(disposing);
        }
    }
}
