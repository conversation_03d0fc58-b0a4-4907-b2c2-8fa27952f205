using System;
using System.Collections.Generic;
using NUnit.Framework;
using Moq;
using SmartConnector.Ews;
using SmartConnector.Ews.Interfaces;
using SmartConnector.Core.Interfaces;
using WeatherExtension.Servers;

namespace WeatherExtension.Tests.Servers
{
    [TestFixture]
    public class WeatherEwsServerTests
    {
        private WeatherEwsServer _server;
        private Mock<IEwsServerContext> _mockContext;
        private Mock<ILogger> _mockLogger;

        [SetUp]
        public void SetUp()
        {
            _server = new WeatherEwsServer();
            _mockContext = new Mock<IEwsServerContext>();
            _mockLogger = new Mock<ILogger>();

            _mockContext.Setup(c => c.GetService<ILogger>())
                       .Returns(_mockLogger.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _server?.Dispose();
        }

        [Test]
        public void Initialize_WithValidContext_SetsUpLogger()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => _server.Initialize(_mockContext.Object));
        }

        [Test]
        public void Initialize_WithNullLogger_ThrowsInvalidOperationException()
        {
            // Arrange
            _mockContext.Setup(c => c.GetService<ILogger>())
                       .Returns((ILogger)null);

            // Act & Assert
            var ex = Assert.Throws<InvalidOperationException>(
                () => _server.Initialize(_mockContext.Object));
            
            Assert.That(ex.Message, Does.Contain("Logger service not available"));
        }

        [Test]
        public void GetValues_WithEmptyRequest_ReturnsEmptyResponse()
        {
            // Arrange
            _server.Initialize(_mockContext.Object);
            var request = new EwsRequest { Items = new List<EwsRequestItem>() };

            // Act
            var response = _server.GetValues(request);

            // Assert
            Assert.IsNotNull(response);
            Assert.IsNotNull(response.Items);
            Assert.That(response.Items.Count, Is.EqualTo(0));
        }

        [Test]
        public void GetValues_WithNullItems_ReturnsEmptyResponse()
        {
            // Arrange
            _server.Initialize(_mockContext.Object);
            var request = new EwsRequest { Items = null };

            // Act
            var response = _server.GetValues(request);

            // Assert
            Assert.IsNotNull(response);
            Assert.IsNotNull(response.Items);
            Assert.That(response.Items.Count, Is.EqualTo(0));
        }

        [Test]
        public void GetValues_WithNonExistentDataPoint_ReturnsBadQuality()
        {
            // Arrange
            _server.Initialize(_mockContext.Object);
            var request = new EwsRequest 
            { 
                Items = new List<EwsRequestItem> 
                { 
                    new EwsRequestItem { ItemName = "NonExistent_Temperature" }
                }
            };

            // Act
            var response = _server.GetValues(request);

            // Assert
            Assert.IsNotNull(response);
            Assert.That(response.Items.Count, Is.EqualTo(1));
            Assert.That(response.Items[0].Quality, Is.EqualTo(EwsQuality.Bad));
            Assert.That(response.Items[0].ErrorMessage, Is.EqualTo("Item not found"));
        }

        [Test]
        public void GetValues_WithExistingDataPoint_ReturnsGoodQuality()
        {
            // Arrange
            _server.Initialize(_mockContext.Object);
            
            var dataPoint = new EwsDataPoint("London_Temperature", "Temperature", "°C", EwsDataType.Float)
            {
                Value = 20.5,
                LastUpdated = DateTime.UtcNow
            };
            _server.AddDataPoint(dataPoint);

            var request = new EwsRequest 
            { 
                Items = new List<EwsRequestItem> 
                { 
                    new EwsRequestItem { ItemName = "London_Temperature" }
                }
            };

            // Act
            var response = _server.GetValues(request);

            // Assert
            Assert.IsNotNull(response);
            Assert.That(response.Items.Count, Is.EqualTo(1));
            Assert.That(response.Items[0].Quality, Is.EqualTo(EwsQuality.Good));
            Assert.That(response.Items[0].Value, Is.EqualTo(20.5));
            Assert.That(response.Items[0].ItemName, Is.EqualTo("London_Temperature"));
        }

        [Test]
        public void SetValues_WithAnyDataPoint_ReturnsBadQuality()
        {
            // Arrange
            _server.Initialize(_mockContext.Object);
            
            var dataPoint = new EwsDataPoint("London_Temperature", "Temperature", "°C", EwsDataType.Float);
            _server.AddDataPoint(dataPoint);

            var request = new EwsRequest 
            { 
                Items = new List<EwsRequestItem> 
                { 
                    new EwsRequestItem { ItemName = "London_Temperature", Value = 25.0 }
                }
            };

            // Act
            var response = _server.SetValues(request);

            // Assert
            Assert.IsNotNull(response);
            Assert.That(response.Items.Count, Is.EqualTo(1));
            Assert.That(response.Items[0].Quality, Is.EqualTo(EwsQuality.Bad));
            Assert.That(response.Items[0].ErrorMessage, Is.EqualTo("Weather data is read-only"));
        }

        [Test]
        public void GetItemList_WithNoDataPoints_ReturnsEmptyList()
        {
            // Arrange
            _server.Initialize(_mockContext.Object);
            var request = new EwsRequest();

            // Act
            var response = _server.GetItemList(request);

            // Assert
            Assert.IsNotNull(response);
            Assert.IsNotNull(response.Items);
            Assert.That(response.Items.Count, Is.EqualTo(0));
        }

        [Test]
        public void GetItemList_WithDataPoints_ReturnsAllDataPoints()
        {
            // Arrange
            _server.Initialize(_mockContext.Object);
            
            var dataPoint1 = new EwsDataPoint("London_Temperature", "Temperature", "°C", EwsDataType.Float)
            {
                Value = 20.5,
                LastUpdated = DateTime.UtcNow
            };
            var dataPoint2 = new EwsDataPoint("London_Humidity", "Humidity", "%", EwsDataType.Integer)
            {
                Value = 65,
                LastUpdated = DateTime.UtcNow
            };
            
            _server.AddDataPoint(dataPoint1);
            _server.AddDataPoint(dataPoint2);

            var request = new EwsRequest();

            // Act
            var response = _server.GetItemList(request);

            // Assert
            Assert.IsNotNull(response);
            Assert.That(response.Items.Count, Is.EqualTo(2));
            
            var tempItem = response.Items.Find(i => i.ItemName == "London_Temperature");
            var humidityItem = response.Items.Find(i => i.ItemName == "London_Humidity");
            
            Assert.IsNotNull(tempItem);
            Assert.IsNotNull(humidityItem);
            Assert.That(tempItem.Value, Is.EqualTo(20.5));
            Assert.That(humidityItem.Value, Is.EqualTo(65));
        }

        [Test]
        public void UpdateDataPoint_WithExistingDataPoint_UpdatesValue()
        {
            // Arrange
            _server.Initialize(_mockContext.Object);
            
            var dataPoint = new EwsDataPoint("London_Temperature", "Temperature", "°C", EwsDataType.Float)
            {
                Value = 20.5,
                LastUpdated = DateTime.UtcNow.AddMinutes(-5)
            };
            _server.AddDataPoint(dataPoint);

            var newTimestamp = DateTime.UtcNow;

            // Act
            _server.UpdateDataPoint("London_Temperature", 25.0, newTimestamp);

            // Verify by getting the value
            var request = new EwsRequest 
            { 
                Items = new List<EwsRequestItem> 
                { 
                    new EwsRequestItem { ItemName = "London_Temperature" }
                }
            };
            var response = _server.GetValues(request);

            // Assert
            Assert.That(response.Items[0].Value, Is.EqualTo(25.0));
            Assert.That(response.Items[0].Timestamp, Is.EqualTo(newTimestamp));
        }

        [Test]
        public void UpdateDataPoint_WithNonExistentDataPoint_LogsWarning()
        {
            // Arrange
            _server.Initialize(_mockContext.Object);

            // Act
            _server.UpdateDataPoint("NonExistent_Temperature", 25.0, DateTime.UtcNow);

            // Assert
            _mockLogger.Verify(l => l.Warning(It.Is<string>(s => s.Contains("non-existent data point"))), Times.Once);
        }

        [Test]
        public void AddDataPoint_WithNewDataPoint_AddsSuccessfully()
        {
            // Arrange
            _server.Initialize(_mockContext.Object);
            var dataPoint = new EwsDataPoint("Test_Temperature", "Temperature", "°C", EwsDataType.Float);

            // Act
            _server.AddDataPoint(dataPoint);

            // Verify by getting item list
            var response = _server.GetItemList(new EwsRequest());

            // Assert
            Assert.That(response.Items.Count, Is.EqualTo(1));
            Assert.That(response.Items[0].ItemName, Is.EqualTo("Test_Temperature"));
        }

        [Test]
        public void Dispose_ClearsDataPoints()
        {
            // Arrange
            _server.Initialize(_mockContext.Object);
            var dataPoint = new EwsDataPoint("Test_Temperature", "Temperature", "°C", EwsDataType.Float);
            _server.AddDataPoint(dataPoint);

            // Act
            _server.Dispose();

            // Assert - should not throw and should log disposal
            _mockLogger.Verify(l => l.Info(It.Is<string>(s => s.Contains("Disposing Weather EWS Server"))), Times.Once);
        }
    }
}
