# SmartConnector Weather Extension Build Script
# This script builds the Weather Extension with proper configuration

param(
    [Parameter(Mandatory=$false)]
    [string]$Configuration = "Release",
    
    [Parameter(Mandatory=$false)]
    [switch]$RunTests = $true,
    
    [Parameter(Mandatory=$false)]
    [switch]$GenerateStrongName = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$Clean = $false
)

Write-Host "=== SmartConnector Weather Extension Build ===" -ForegroundColor Green
Write-Host "Configuration: $Configuration" -ForegroundColor Yellow

$SolutionPath = Join-Path $PSScriptRoot "..\WeatherExtension.sln"
$ProjectPath = Join-Path $PSScriptRoot "..\WeatherExtension\WeatherExtension.csproj"
$TestProjectPath = Join-Path $PSScriptRoot "..\WeatherExtension.Tests\WeatherExtension.Tests.csproj"
$StrongNameKeyPath = Join-Path $PSScriptRoot "..\WeatherExtension\WeatherExtension.snk"

# Validate solution exists
if (-not (Test-Path $SolutionPath)) {
    Write-Error "Solution file not found: $SolutionPath"
    exit 1
}

try {
    # Generate strong name key if requested
    if ($GenerateStrongName) {
        Write-Host "Generating strong name key..." -ForegroundColor Yellow
        
        # Check if sn.exe is available
        $SnPath = Get-Command "sn.exe" -ErrorAction SilentlyContinue
        if (-not $SnPath) {
            # Try to find sn.exe in common locations
            $PossiblePaths = @(
                "${env:ProgramFiles(x86)}\Microsoft SDKs\Windows\v10.0A\bin\NETFX 4.8 Tools\sn.exe",
                "${env:ProgramFiles}\Microsoft SDKs\Windows\v10.0A\bin\NETFX 4.8 Tools\sn.exe",
                "${env:ProgramFiles(x86)}\Windows Kits\10\bin\*\x64\sn.exe"
            )
            
            foreach ($Path in $PossiblePaths) {
                $ResolvedPath = Get-ChildItem $Path -ErrorAction SilentlyContinue | Select-Object -First 1
                if ($ResolvedPath) {
                    $SnPath = $ResolvedPath.FullName
                    break
                }
            }
        }
        
        if ($SnPath) {
            & $SnPath -k $StrongNameKeyPath
            Write-Host "Strong name key generated: $StrongNameKeyPath" -ForegroundColor Green
        } else {
            Write-Warning "sn.exe not found. Please install Windows SDK or Visual Studio."
            Write-Host "Continuing with placeholder key file..." -ForegroundColor Yellow
        }
    }

    # Clean if requested
    if ($Clean) {
        Write-Host "Cleaning solution..." -ForegroundColor Yellow
        dotnet clean $SolutionPath --configuration $Configuration
    }

    # Restore NuGet packages
    Write-Host "Restoring NuGet packages..." -ForegroundColor Yellow
    dotnet restore $SolutionPath
    if ($LASTEXITCODE -ne 0) {
        throw "NuGet restore failed"
    }

    # Build solution
    Write-Host "Building solution..." -ForegroundColor Yellow
    dotnet build $SolutionPath --configuration $Configuration --no-restore
    if ($LASTEXITCODE -ne 0) {
        throw "Build failed"
    }

    Write-Host "Build completed successfully" -ForegroundColor Green

    # Run tests if requested
    if ($RunTests) {
        Write-Host "Running unit tests..." -ForegroundColor Yellow
        dotnet test $TestProjectPath --configuration $Configuration --no-build --verbosity normal
        if ($LASTEXITCODE -ne 0) {
            Write-Warning "Some tests failed. Check test output above."
        } else {
            Write-Host "All tests passed" -ForegroundColor Green
        }
    }

    # Display build output information
    $BuildOutputPath = Join-Path $PSScriptRoot "..\WeatherExtension\bin\$Configuration"
    $MainAssembly = Join-Path $BuildOutputPath "WeatherExtension.dll"
    
    if (Test-Path $MainAssembly) {
        $AssemblyInfo = Get-Item $MainAssembly
        Write-Host ""
        Write-Host "Build Output:" -ForegroundColor Cyan
        Write-Host "  Assembly: $MainAssembly" -ForegroundColor White
        Write-Host "  Size: $([math]::Round($AssemblyInfo.Length / 1KB, 2)) KB" -ForegroundColor White
        Write-Host "  Modified: $($AssemblyInfo.LastWriteTime)" -ForegroundColor White
        
        # Check if assembly is strong-named
        try {
            $AssemblyName = [System.Reflection.AssemblyName]::GetAssemblyName($MainAssembly)
            if ($AssemblyName.GetPublicKey().Length -gt 0) {
                Write-Host "  Strong-named: Yes" -ForegroundColor Green
            } else {
                Write-Host "  Strong-named: No" -ForegroundColor Yellow
            }
        } catch {
            Write-Host "  Strong-named: Unable to determine" -ForegroundColor Yellow
        }
    }

    Write-Host ""
    Write-Host "=== Build Completed Successfully ===" -ForegroundColor Green
    Write-Host ""
    Write-Host "Next Steps:" -ForegroundColor Cyan
    Write-Host "1. Deploy using: .\Scripts\Deploy.ps1 -SmartConnectorPath 'C:\Program Files\SmartConnector'" -ForegroundColor White
    Write-Host "2. Or manually copy WeatherExtension.dll to SmartConnector Extensions folder" -ForegroundColor White

} catch {
    Write-Error "Build failed: $($_.Exception.Message)"
    exit 1
}
