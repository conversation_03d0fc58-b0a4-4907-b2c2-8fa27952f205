# Weather Extension for SmartConnector

A comprehensive SmartConnector extension that retrieves weather data from the OpenWeatherMap API and serves it through EWS (Enterprise Web Services) to connected clients.

## Overview

This extension demonstrates the complete implementation of a SmartConnector extension following the official developer guide. It includes:

- **SetupProcessor**: Creates and provisions EWS server data structure
- **UpdateProcessor**: Retrieves weather data from OpenWeatherMap API asynchronously
- **Custom EWS Server**: Serves weather data to EWS clients
- **Comprehensive Unit Tests**: Full test coverage for all components
- **Licensing Support**: Integration with SmartConnector licensing system
- **Configuration Management**: JSON-based processor configurations

## Features

- Real-time weather data from OpenWeatherMap API
- Support for multiple cities simultaneously
- Configurable update intervals
- Temperature units support (metric, imperial, kelvin)
- Comprehensive error handling and logging
- Read-only data protection
- Strong-named assembly for security
- Full licensing integration

## Weather Data Points

For each configured city, the extension provides:

- **Temperature**: Current temperature
- **Humidity**: Relative humidity percentage
- **Pressure**: Atmospheric pressure
- **Wind Speed**: Wind speed
- **Wind Direction**: Wind direction in degrees
- **Description**: Weather condition description
- **Last Updated**: Timestamp of last data update

## Prerequisites

- SmartConnector 2.0.0 or higher
- .NET Framework 4.8
- OpenWeatherMap API key (free registration at https://openweathermap.org/api)
- Visual Studio 2019 or higher (for development)

## Quick Start

### 1. Get OpenWeatherMap API Key

1. Register at https://openweathermap.org/api
2. Create a free API key
3. Note the API key for configuration

### 2. Build the Extension

```bash
# Clone or download the source code
cd WeatherExtension

# Restore NuGet packages
dotnet restore

# Build the solution
dotnet build --configuration Release
```

### 3. Deploy to SmartConnector

1. Copy `WeatherExtension.dll` to SmartConnector Extensions folder
2. Copy configuration files to appropriate directories
3. Restart SmartConnector service

### 4. Configure Processors

1. Open SmartConnector Portal
2. Navigate to Processor Configuration
3. Import `WeatherSetupConfig.json` and `WeatherUpdateConfig.json`
4. Set your OpenWeatherMap API key in WeatherUpdate processor
5. Configure cities to monitor
6. Enable and run WeatherSetup processor first
7. Enable WeatherUpdate processor for automatic updates

## Configuration

### WeatherSetup Processor

```json
{
  "ServerName": "WeatherServer",
  "Cities": "London,New York,Tokyo,Paris,Sydney",
  "Description": "Weather data from OpenWeatherMap API"
}
```

### WeatherUpdate Processor

```json
{
  "ApiKey": "your-openweathermap-api-key",
  "ServerName": "WeatherServer",
  "Cities": "London,New York,Tokyo,Paris,Sydney",
  "Units": "metric",
  "UpdateIntervalMinutes": 15
}
```

## Testing

### Run Unit Tests

```bash
# Run all tests
dotnet test

# Run with coverage
dotnet test --collect:"XPlat Code Coverage"

# Run specific test category
dotnet test --filter Category=Unit
```

### Integration Testing

For integration tests with real API calls:

```bash
# Set environment variable with valid API key
set OPENWEATHER_API_KEY=your-actual-api-key

# Run integration tests
dotnet test --filter Category=Integration
```

## EWS Client Usage

### SoapUI Example

1. Create new SOAP project with WSDL: `http://localhost:8080/WeatherServer?wsdl`
2. Use GetValues request:

```xml
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <GetValues xmlns="http://schemas.schneider-electric.com/smartconnector/ews">
      <Items>
        <Item>
          <ItemName>London_Temperature</ItemName>
        </Item>
        <Item>
          <ItemName>London_Humidity</ItemName>
        </Item>
      </Items>
    </GetValues>
  </soap:Body>
</soap:Envelope>
```

### C# Client Example

```csharp
var client = new WeatherEwsClient("http://localhost:8080/WeatherServer");
var values = await client.GetValuesAsync(new[] { "London_Temperature", "London_Humidity" });

foreach (var value in values)
{
    Console.WriteLine($"{value.ItemName}: {value.Value} {value.Units}");
}
```

## Troubleshooting

### Common Issues

1. **API Key Invalid**: Verify OpenWeatherMap API key is correct and active
2. **Server Not Found**: Ensure WeatherSetup processor ran successfully first
3. **No Data Updates**: Check network connectivity and API rate limits
4. **License Errors**: Verify SmartConnector licensing is properly configured

### Logging

Check SmartConnector logs for detailed error information:
- Location: `%ProgramData%\SmartConnector\Logs\`
- Filter by: `WeatherExtension`

## Development

### Project Structure

```
WeatherExtension/
├── Models/                 # Data models
├── Services/              # External API services
├── Processors/            # SmartConnector processors
├── Servers/               # EWS server implementations
├── Licensing/             # License validation
├── Configuration/         # Processor configurations
└── Properties/            # Assembly information

WeatherExtension.Tests/
├── Services/              # Service unit tests
├── Processors/            # Processor unit tests
└── Servers/               # Server unit tests
```

### Adding New Cities

1. Update processor configuration
2. Restart WeatherUpdate processor
3. Data points will be created automatically

### Extending Data Points

1. Modify `WeatherSetupProcessor.SetupCityDataStructure()`
2. Update `WeatherUpdateProcessor.UpdateEwsDataPoints()`
3. Add corresponding unit tests

## License

This extension requires a valid SmartConnector license. Contact Schneider Electric for licensing information.

## Support

For technical support:
- SmartConnector Documentation: [Official Guide]
- OpenWeatherMap API: https://openweathermap.org/api/documentation
- Issues: Create GitHub issue with detailed description

## Version History

- **1.0.0**: Initial release with core weather data functionality
