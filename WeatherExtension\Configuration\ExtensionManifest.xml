<?xml version="1.0" encoding="utf-8"?>
<ExtensionManifest xmlns="http://schemas.schneider-electric.com/smartconnector/extension/manifest">
  <Extension>
    <Name>WeatherExtension</Name>
    <Version>1.0.0</Version>
    <Description>SmartConnector extension for retrieving and serving weather data from OpenWeatherMap API</Description>
    <Author>Your Company</Author>
    <Copyright>Copyright © Your Company 2025</Copyright>
    <License>Commercial</License>
    <MinimumSmartConnectorVersion>2.0.0</MinimumSmartConnectorVersion>
    <SupportedPlatforms>
      <Platform>Windows</Platform>
    </SupportedPlatforms>
    
    <Dependencies>
      <Dependency>
        <Name>Newtonsoft.Json</Name>
        <Version>13.0.3</Version>
        <Required>true</Required>
      </Dependency>
      <Dependency>
        <Name>System.Net.Http</Name>
        <Version>4.3.4</Version>
        <Required>true</Required>
      </Dependency>
    </Dependencies>

    <Assemblies>
      <Assembly>
        <Name>WeatherExtension.dll</Name>
        <Path>WeatherExtension.dll</Path>
        <StrongNamed>true</StrongNamed>
      </Assembly>
    </Assemblies>

    <Processors>
      <Processor>
        <Name>WeatherSetup</Name>
        <Type>WeatherExtension.Processors.WeatherSetupProcessor</Type>
        <Description>Sets up weather data EWS server structure</Description>
        <Category>Setup</Category>
        <ConfigurationFile>Configuration/WeatherSetupConfig.json</ConfigurationFile>
      </Processor>
      <Processor>
        <Name>WeatherUpdate</Name>
        <Type>WeatherExtension.Processors.WeatherUpdateProcessor</Type>
        <Description>Updates weather data from OpenWeatherMap API</Description>
        <Category>Data Update</Category>
        <ConfigurationFile>Configuration/WeatherUpdateConfig.json</ConfigurationFile>
      </Processor>
    </Processors>

    <EwsServers>
      <EwsServer>
        <Name>WeatherEwsServer</Name>
        <Type>WeatherExtension.Servers.WeatherEwsServer</Type>
        <Description>EWS server for weather data</Description>
        <DefaultServerName>WeatherServer</DefaultServerName>
      </EwsServer>
    </EwsServers>

    <Licensing>
      <Required>true</Required>
      <ExtensionId>WeatherExtension</ExtensionId>
      <ValidatorType>WeatherExtension.Licensing.WeatherLicenseValidator</ValidatorType>
    </Licensing>

    <Installation>
      <PreInstallScript>Scripts/PreInstall.ps1</PreInstallScript>
      <PostInstallScript>Scripts/PostInstall.ps1</PostInstallScript>
      <UninstallScript>Scripts/Uninstall.ps1</UninstallScript>
    </Installation>

    <Documentation>
      <UserGuide>Documentation/UserGuide.md</UserGuide>
      <ApiReference>Documentation/ApiReference.md</ApiReference>
      <ReleaseNotes>Documentation/ReleaseNotes.md</ReleaseNotes>
    </Documentation>
  </Extension>
</ExtensionManifest>
