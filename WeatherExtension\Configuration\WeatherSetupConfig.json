{"ProcessorConfiguration": {"Name": "WeatherSetup", "Description": "Sets up weather data EWS server structure", "ProcessorType": "WeatherExtension.Processors.WeatherSetupProcessor", "Assembly": "WeatherExtension.dll", "Enabled": true, "Schedule": {"Type": "Manual", "Description": "Run manually to set up weather data structure"}, "Values": {"ServerName": {"Value": "WeatherServer", "Description": "Name of the EWS server to create"}, "Cities": {"Value": "London,New York,Tokyo,Paris,Sydney", "Description": "Comma-separated list of cities to monitor"}, "Description": {"Value": "Weather data from OpenWeatherMap API", "Description": "Description of the weather server"}}, "Dependencies": [], "Tags": ["Setup", "Weather", "EWS"], "Category": "Weather Extension"}}