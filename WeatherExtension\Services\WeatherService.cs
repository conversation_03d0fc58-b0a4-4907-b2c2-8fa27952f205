using System;
using System.Net.Http;
using System.Threading.Tasks;
using Newtonsoft.Json;
using WeatherExtension.Models;

namespace WeatherExtension.Services
{
    /// <summary>
    /// Service for retrieving weather data from OpenWeatherMap API
    /// </summary>
    public class WeatherService : IDisposable
    {
        private readonly HttpClient _httpClient;
        private readonly string _apiKey;
        private readonly string _baseUrl = "https://api.openweathermap.org/data/2.5/weather";

        public WeatherService(string apiKey)
        {
            _apiKey = apiKey ?? throw new ArgumentNullException(nameof(apiKey));
            _httpClient = new HttpClient();
            _httpClient.Timeout = TimeSpan.FromSeconds(30);
        }

        /// <summary>
        /// Gets weather data for the specified city
        /// </summary>
        /// <param name="city">City name</param>
        /// <param name="units">Temperature units (metric, imperial, kelvin)</param>
        /// <returns>Weather data</returns>
        public async Task<WeatherData> GetWeatherAsync(string city, string units = "metric")
        {
            if (string.IsNullOrWhiteSpace(city))
                throw new ArgumentException("City name cannot be null or empty", nameof(city));

            var url = $"{_baseUrl}?q={Uri.EscapeDataString(city)}&appid={_apiKey}&units={units}";

            try
            {
                var response = await _httpClient.GetAsync(url);
                response.EnsureSuccessStatusCode();

                var json = await response.Content.ReadAsStringAsync();
                var weatherData = JsonConvert.DeserializeObject<WeatherData>(json);

                return weatherData;
            }
            catch (HttpRequestException ex)
            {
                throw new InvalidOperationException($"Failed to retrieve weather data for {city}: {ex.Message}", ex);
            }
            catch (JsonException ex)
            {
                throw new InvalidOperationException($"Failed to parse weather data response: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Gets weather data by coordinates
        /// </summary>
        /// <param name="latitude">Latitude</param>
        /// <param name="longitude">Longitude</param>
        /// <param name="units">Temperature units</param>
        /// <returns>Weather data</returns>
        public async Task<WeatherData> GetWeatherByCoordinatesAsync(double latitude, double longitude, string units = "metric")
        {
            var url = $"{_baseUrl}?lat={latitude}&lon={longitude}&appid={_apiKey}&units={units}";

            try
            {
                var response = await _httpClient.GetAsync(url);
                response.EnsureSuccessStatusCode();

                var json = await response.Content.ReadAsStringAsync();
                var weatherData = JsonConvert.DeserializeObject<WeatherData>(json);

                return weatherData;
            }
            catch (HttpRequestException ex)
            {
                throw new InvalidOperationException($"Failed to retrieve weather data for coordinates ({latitude}, {longitude}): {ex.Message}", ex);
            }
            catch (JsonException ex)
            {
                throw new InvalidOperationException($"Failed to parse weather data response: {ex.Message}", ex);
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
