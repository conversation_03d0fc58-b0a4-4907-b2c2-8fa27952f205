using System;
using System.Threading.Tasks;
using NUnit.Framework;
using WeatherExtension.Services;
using WeatherExtension.Models;

namespace WeatherExtension.Tests.Services
{
    [TestFixture]
    public class WeatherServiceTests
    {
        private WeatherService _weatherService;
        private const string TestApiKey = "test-api-key-12345";

        [SetUp]
        public void SetUp()
        {
            _weatherService = new WeatherService(TestApiKey);
        }

        [TearDown]
        public void TearDown()
        {
            _weatherService?.Dispose();
        }

        [Test]
        public void Constructor_WithNullApiKey_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new WeatherService(null));
        }

        [Test]
        public void Constructor_WithEmptyApiKey_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new WeatherService(""));
        }

        [Test]
        public void Constructor_WithValidApiKey_CreatesInstance()
        {
            // Act
            using var service = new WeatherService(TestApiKey);

            // Assert
            Assert.IsNotNull(service);
        }

        [Test]
        public async Task GetWeatherAsync_WithNullCity_ThrowsArgumentException()
        {
            // Act & Assert
            var ex = await Assert.ThrowsAsync<ArgumentException>(
                () => _weatherService.GetWeatherAsync(null));
            
            Assert.That(ex.ParamName, Is.EqualTo("city"));
        }

        [Test]
        public async Task GetWeatherAsync_WithEmptyCity_ThrowsArgumentException()
        {
            // Act & Assert
            var ex = await Assert.ThrowsAsync<ArgumentException>(
                () => _weatherService.GetWeatherAsync(""));
            
            Assert.That(ex.ParamName, Is.EqualTo("city"));
        }

        [Test]
        public async Task GetWeatherAsync_WithWhitespaceCity_ThrowsArgumentException()
        {
            // Act & Assert
            var ex = await Assert.ThrowsAsync<ArgumentException>(
                () => _weatherService.GetWeatherAsync("   "));
            
            Assert.That(ex.ParamName, Is.EqualTo("city"));
        }

        [Test]
        public async Task GetWeatherByCoordinatesAsync_WithValidCoordinates_BuildsCorrectUrl()
        {
            // Note: This test would require mocking HttpClient to verify the URL
            // For now, we'll test that the method doesn't throw with valid inputs
            
            // Act & Assert
            // This will fail with actual API call, but validates parameter handling
            var ex = await Assert.ThrowsAsync<InvalidOperationException>(
                () => _weatherService.GetWeatherByCoordinatesAsync(51.5074, -0.1278));
            
            Assert.That(ex.Message, Does.Contain("Failed to retrieve weather data"));
        }

        [Test]
        public void Dispose_CanBeCalledMultipleTimes()
        {
            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                _weatherService.Dispose();
                _weatherService.Dispose();
            });
        }

        [Test]
        public async Task GetWeatherAsync_WithInvalidApiKey_ThrowsInvalidOperationException()
        {
            // Act & Assert
            var ex = await Assert.ThrowsAsync<InvalidOperationException>(
                () => _weatherService.GetWeatherAsync("London"));
            
            Assert.That(ex.Message, Does.Contain("Failed to retrieve weather data"));
        }

        // Integration test - requires valid API key and internet connection
        [Test, Explicit("Integration test requiring valid API key")]
        public async Task GetWeatherAsync_WithValidApiKeyAndCity_ReturnsWeatherData()
        {
            // Arrange
            const string validApiKey = "your-valid-api-key-here";
            using var service = new WeatherService(validApiKey);

            // Act
            var result = await service.GetWeatherAsync("London");

            // Assert
            Assert.IsNotNull(result);
            Assert.IsNotNull(result.Location);
            Assert.IsNotNull(result.Main);
            Assert.That(result.Location, Is.EqualTo("London"));
        }
    }
}
