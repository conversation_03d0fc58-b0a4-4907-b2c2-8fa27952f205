using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SmartConnector.Core;
using SmartConnector.Core.Attributes;
using SmartConnector.Core.Interfaces;
using SmartConnector.Ews.Interfaces;
using WeatherExtension.Services;
using WeatherExtension.Models;

namespace WeatherExtension.Processors
{
    /// <summary>
    /// Processor that updates weather data asynchronously from external API
    /// </summary>
    [Processor("WeatherUpdate", "Updates weather data from OpenWeatherMap API")]
    public class WeatherUpdateProcessor : IProcessor
    {
        private IEwsServerManager _ewsServerManager;
        private ILogger _logger;
        private WeatherService _weatherService;

        [ProcessorValue("ApiKey", "OpenWeatherMap API key", "")]
        public string ApiKey { get; set; }

        [ProcessorValue("ServerName", "Name of the EWS server to update", "WeatherServer")]
        public string ServerName { get; set; } = "WeatherServer";

        [ProcessorValue("Cities", "Comma-separated list of cities to update", "London,New York,Tokyo")]
        public string Cities { get; set; } = "London,New York,Tokyo";

        [ProcessorValue("Units", "Temperature units (metric, imperial, kelvin)", "metric")]
        public string Units { get; set; } = "metric";

        [ProcessorValue("UpdateIntervalMinutes", "Update interval in minutes", "15")]
        public int UpdateIntervalMinutes { get; set; } = 15;

        public void Initialize(IProcessorContext context)
        {
            _ewsServerManager = context.GetService<IEwsServerManager>();
            _logger = context.GetService<ILogger>();
            
            if (_ewsServerManager == null)
                throw new InvalidOperationException("EWS Server Manager service not available");
            
            if (_logger == null)
                throw new InvalidOperationException("Logger service not available");

            if (string.IsNullOrWhiteSpace(ApiKey))
                throw new InvalidOperationException("OpenWeatherMap API key is required");

            _weatherService = new WeatherService(ApiKey);
        }

        public ProcessorResult Execute()
        {
            try
            {
                _logger.Info("Starting weather data update");

                // Check if server exists
                if (!_ewsServerManager.ServerExists(ServerName))
                {
                    _logger.Error($"EWS server {ServerName} does not exist. Run setup processor first.");
                    return ProcessorResult.Failure($"EWS server {ServerName} not found");
                }

                var cityList = ParseCities(Cities);
                var updateTasks = new List<Task>();

                foreach (var city in cityList)
                {
                    updateTasks.Add(UpdateCityWeatherAsync(city));
                }

                // Wait for all updates to complete
                Task.WaitAll(updateTasks.ToArray(), TimeSpan.FromMinutes(2));

                _logger.Info($"Weather data update completed for {cityList.Count} cities");
                return ProcessorResult.Success($"Updated weather data for {cityList.Count} cities");
            }
            catch (Exception ex)
            {
                _logger.Error($"Error updating weather data: {ex.Message}", ex);
                return ProcessorResult.Failure($"Update failed: {ex.Message}");
            }
        }

        private async Task UpdateCityWeatherAsync(string city)
        {
            try
            {
                _logger.Debug($"Updating weather data for {city}");

                var weatherData = await _weatherService.GetWeatherAsync(city, Units);
                
                if (weatherData != null)
                {
                    UpdateEwsDataPoints(city, weatherData);
                    _logger.Debug($"Successfully updated weather data for {city}");
                }
                else
                {
                    _logger.Warning($"No weather data received for {city}");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"Failed to update weather data for {city}: {ex.Message}", ex);
            }
        }

        private void UpdateEwsDataPoints(string city, WeatherData weatherData)
        {
            var cityKey = city.Replace(" ", "_").Replace(",", "");
            var timestamp = DateTime.UtcNow;

            try
            {
                // Update temperature
                _ewsServerManager.UpdateDataPoint(ServerName, $"{cityKey}_Temperature", 
                    weatherData.Main.Temperature, timestamp);

                // Update humidity
                _ewsServerManager.UpdateDataPoint(ServerName, $"{cityKey}_Humidity", 
                    weatherData.Main.Humidity, timestamp);

                // Update pressure
                _ewsServerManager.UpdateDataPoint(ServerName, $"{cityKey}_Pressure", 
                    weatherData.Main.Pressure, timestamp);

                // Update wind data
                if (weatherData.Wind != null)
                {
                    _ewsServerManager.UpdateDataPoint(ServerName, $"{cityKey}_WindSpeed", 
                        weatherData.Wind.Speed, timestamp);
                    
                    _ewsServerManager.UpdateDataPoint(ServerName, $"{cityKey}_WindDirection", 
                        weatherData.Wind.Direction, timestamp);
                }

                // Update weather description
                if (weatherData.Weather != null && weatherData.Weather.Length > 0)
                {
                    _ewsServerManager.UpdateDataPoint(ServerName, $"{cityKey}_Description", 
                        weatherData.Weather[0].Description, timestamp);
                }

                // Update last updated timestamp
                _ewsServerManager.UpdateDataPoint(ServerName, $"{cityKey}_LastUpdated", 
                    timestamp, timestamp);

                _logger.Debug($"Updated all data points for {city}");
            }
            catch (Exception ex)
            {
                _logger.Error($"Error updating EWS data points for {city}: {ex.Message}", ex);
                throw;
            }
        }

        private List<string> ParseCities(string citiesString)
        {
            var cities = new List<string>();
            if (!string.IsNullOrWhiteSpace(citiesString))
            {
                var cityArray = citiesString.Split(',');
                foreach (var city in cityArray)
                {
                    var trimmedCity = city.Trim();
                    if (!string.IsNullOrEmpty(trimmedCity))
                    {
                        cities.Add(trimmedCity);
                    }
                }
            }
            return cities;
        }

        public void Dispose()
        {
            _weatherService?.Dispose();
        }
    }
}
