using System;
using SmartConnector.Licensing;
using SmartConnector.Core.Interfaces;

namespace WeatherExtension.Licensing
{
    /// <summary>
    /// License validator for the Weather Extension
    /// </summary>
    public class WeatherLicenseValidator
    {
        private readonly ILogger _logger;
        private readonly string _extensionId = "WeatherExtension";
        private readonly string _version = "1.0.0";

        public WeatherLicenseValidator(ILogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Validates the license for the weather extension
        /// </summary>
        /// <returns>True if license is valid, false otherwise</returns>
        public bool ValidateLicense()
        {
            try
            {
                _logger.Info("Validating Weather Extension license");

                var licenseManager = LicenseManager.Instance;
                var licenseInfo = licenseManager.GetLicense(_extensionId);

                if (licenseInfo == null)
                {
                    _logger.Warning("No license found for Weather Extension");
                    return false;
                }

                if (!licenseInfo.IsValid)
                {
                    _logger.Warning($"Invalid license for Weather Extension: {licenseInfo.ErrorMessage}");
                    return false;
                }

                if (licenseInfo.ExpirationDate.HasValue && licenseInfo.ExpirationDate.Value < DateTime.UtcNow)
                {
                    _logger.Warning($"License for Weather Extension has expired: {licenseInfo.ExpirationDate.Value}");
                    return false;
                }

                // Check version compatibility
                if (!string.IsNullOrEmpty(licenseInfo.Version) && !IsVersionCompatible(licenseInfo.Version))
                {
                    _logger.Warning($"License version {licenseInfo.Version} is not compatible with extension version {_version}");
                    return false;
                }

                _logger.Info($"Weather Extension license validated successfully. Expires: {licenseInfo.ExpirationDate?.ToString() ?? "Never"}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error($"Error validating Weather Extension license: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// Gets license information
        /// </summary>
        /// <returns>License information or null if not found</returns>
        public LicenseInfo GetLicenseInfo()
        {
            try
            {
                var licenseManager = LicenseManager.Instance;
                return licenseManager.GetLicense(_extensionId);
            }
            catch (Exception ex)
            {
                _logger.Error($"Error retrieving license information: {ex.Message}", ex);
                return null;
            }
        }

        /// <summary>
        /// Checks if the licensed version is compatible with the current extension version
        /// </summary>
        /// <param name="licenseVersion">Licensed version</param>
        /// <returns>True if compatible</returns>
        private bool IsVersionCompatible(string licenseVersion)
        {
            try
            {
                var currentVersion = new Version(_version);
                var licensedVersion = new Version(licenseVersion);

                // Allow same major version
                return currentVersion.Major == licensedVersion.Major;
            }
            catch
            {
                // If version parsing fails, assume compatible
                return true;
            }
        }

        /// <summary>
        /// Validates license and throws exception if invalid
        /// </summary>
        public void ValidateLicenseOrThrow()
        {
            if (!ValidateLicense())
            {
                throw new UnauthorizedAccessException("Valid license required for Weather Extension");
            }
        }
    }
}
