{"ProcessorConfiguration": {"Name": "WeatherUpdate", "Description": "Updates weather data from OpenWeatherMap API", "ProcessorType": "WeatherExtension.Processors.WeatherUpdateProcessor", "Assembly": "WeatherExtension.dll", "Enabled": true, "Schedule": {"Type": "Interval", "IntervalMinutes": 15, "Description": "Update weather data every 15 minutes"}, "Values": {"ApiKey": {"Value": "", "Description": "OpenWeatherMap API key (required)", "Required": true, "Sensitive": true}, "ServerName": {"Value": "WeatherServer", "Description": "Name of the EWS server to update"}, "Cities": {"Value": "London,New York,Tokyo,Paris,Sydney", "Description": "Comma-separated list of cities to update"}, "Units": {"Value": "metric", "Description": "Temperature units (metric, imperial, kelvin)", "AllowedValues": ["metric", "imperial", "kelvin"]}, "UpdateIntervalMinutes": {"Value": 15, "Description": "Update interval in minutes", "MinValue": 5, "MaxValue": 1440}}, "Dependencies": ["WeatherSetup"], "Tags": ["Update", "Weather", "API"], "Category": "Weather Extension", "RetryPolicy": {"MaxRetries": 3, "RetryDelaySeconds": 30, "ExponentialBackoff": true}, "Timeout": {"TimeoutMinutes": 5}}}