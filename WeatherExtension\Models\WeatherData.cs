using System;
using Newtonsoft.Json;

namespace WeatherExtension.Models
{
    /// <summary>
    /// Represents weather data retrieved from external API
    /// </summary>
    public class WeatherData
    {
        [JsonProperty("name")]
        public string Location { get; set; }

        [JsonProperty("main")]
        public MainWeatherData Main { get; set; }

        [JsonProperty("weather")]
        public WeatherDescription[] Weather { get; set; }

        [JsonProperty("wind")]
        public WindData Wind { get; set; }

        [JsonProperty("dt")]
        public long Timestamp { get; set; }

        public DateTime LastUpdated => DateTimeOffset.FromUnixTimeSeconds(Timestamp).DateTime;
    }

    public class MainWeatherData
    {
        [JsonProperty("temp")]
        public double Temperature { get; set; }

        [JsonProperty("feels_like")]
        public double FeelsLike { get; set; }

        [JsonProperty("humidity")]
        public int Humidity { get; set; }

        [JsonProperty("pressure")]
        public double Pressure { get; set; }

        [JsonProperty("temp_min")]
        public double TemperatureMin { get; set; }

        [JsonProperty("temp_max")]
        public double TemperatureMax { get; set; }
    }

    public class WeatherDescription
    {
        [JsonProperty("main")]
        public string Main { get; set; }

        [JsonProperty("description")]
        public string Description { get; set; }

        [JsonProperty("icon")]
        public string Icon { get; set; }
    }

    public class WindData
    {
        [JsonProperty("speed")]
        public double Speed { get; set; }

        [JsonProperty("deg")]
        public int Direction { get; set; }
    }
}
